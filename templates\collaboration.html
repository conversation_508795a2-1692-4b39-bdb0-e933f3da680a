{% extends "base.html" %}

{% block title %}Collaboration - T-Office{% endblock %}

{% block styles %}
<style>
.collaboration-header {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    padding: 2rem;
    margin-bottom: 2rem;
    border: 1px solid var(--gray-200);
}

.collaboration-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
}

.collaboration-subtitle {
    color: var(--gray-600);
    font-size: 1.1rem;
}

.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-full);
    font-weight: 600;
    font-size: 0.875rem;
    margin-top: 1rem;
}

.status-dot {
    width: 8px;
    height: 8px;
    background: var(--success-color);
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.collaboration-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.activity-panel {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.panel-header {
    background: var(--gradient-primary);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    align-items: center;
    justify-content: between;
}

.panel-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    flex: 1;
}

.panel-badge {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-full);
    font-size: 0.75rem;
    font-weight: 600;
}

.panel-body {
    padding: 0;
    max-height: 500px;
    overflow-y: auto;
}

.activity-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.activity-item {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: var(--transition-fast);
}

.activity-item:hover {
    background: var(--gray-50);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-avatar {
    width: 45px;
    height: 45px;
    border-radius: var(--radius-full);
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-text {
    color: var(--gray-800);
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.activity-meta {
    color: var(--gray-500);
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.activity-time {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.activity-status {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.status-online {
    color: var(--success-color);
}

.status-offline {
    color: var(--gray-400);
}

.online-users {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
}

.users-header {
    background: var(--gradient-success);
    color: white;
    padding: 1.5rem 2rem;
}

.users-body {
    padding: 2rem;
}

.user-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 1rem;
}

.user-card {
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    text-align: center;
    transition: var(--transition-normal);
    border: 2px solid transparent;
}

.user-card:hover {
    background: var(--white);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.user-card.online {
    border-color: var(--success-color);
    background: rgba(16, 185, 129, 0.05);
}

.user-avatar-large {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-full);
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.5rem;
    margin: 0 auto 1rem;
    position: relative;
}

.user-avatar-large.online::after {
    content: '';
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 16px;
    height: 16px;
    background: var(--success-color);
    border: 3px solid white;
    border-radius: 50%;
}

.user-name {
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 0.5rem;
}

.user-status {
    font-size: 0.875rem;
    color: var(--gray-600);
    margin-bottom: 0.5rem;
}

.user-activity {
    font-size: 0.75rem;
    color: var(--gray-500);
}

.notifications-panel {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border: 1px solid var(--gray-200);
    overflow: hidden;
    margin-bottom: 2rem;
}

.notifications-header {
    background: var(--gradient-secondary);
    color: white;
    padding: 1.5rem 2rem;
    display: flex;
    align-items: center;
    justify-content: between;
}

.notification-item {
    padding: 1rem 2rem;
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    transition: var(--transition-fast);
}

.notification-item:hover {
    background: var(--gray-50);
}

.notification-item.unread {
    background: rgba(37, 99, 235, 0.05);
    border-left: 4px solid var(--primary-color);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.notification-icon.file {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
}

.notification-icon.user {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
}

.notification-icon.system {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
}

.notification-content {
    flex: 1;
}

.notification-text {
    color: var(--gray-800);
    margin-bottom: 0.25rem;
}

.notification-time {
    color: var(--gray-500);
    font-size: 0.875rem;
}

.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--gray-500);
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

@media (max-width: 768px) {
    .collaboration-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .collaboration-header {
        padding: 1.5rem;
        text-align: center;
    }
    
    .collaboration-title {
        font-size: 2rem;
    }
    
    .user-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
    
    .user-card {
        padding: 1rem;
    }
    
    .user-avatar-large {
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }
    
    .activity-item {
        padding: 1rem;
    }
    
    .activity-avatar {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
    
    .panel-body {
        max-height: 300px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-4">
    <!-- Collaboration Header -->
    <div class="collaboration-header">
        <h1 class="collaboration-title">
            <i class="fas fa-users me-3"></i>Real-time Collaboration
        </h1>
        <p class="collaboration-subtitle">See who's working with files in real-time and stay connected with your team.</p>

        <div class="d-flex justify-content-between align-items-center">
            <div class="status-indicator">
                <div class="status-dot"></div>
                Connected - Real-time updates active
            </div>

            {% if current_user.role == 'Administrator' or current_user.is_admin %}
            <a href="{{ url_for('manage_collaboration_access') }}" class="btn btn-outline-primary btn-sm">
                <i class="fas fa-users-cog me-2"></i>Manage Access
            </a>
            {% endif %}
        </div>
    </div>
    
    <!-- Online Users -->
    <div class="online-users">
        <div class="users-header">
            <h3 class="panel-title">
                <i class="fas fa-user-friends me-2"></i>Online Users
                <span class="panel-badge ms-2">3 Active</span>
            </h3>
        </div>
        <div class="users-body">
            <div class="user-grid">
                <div class="user-card online">
                    <div class="user-avatar-large online">
                        {{ current_user.username[0].upper() }}
                    </div>
                    <div class="user-name">{{ current_user.username }} (You)</div>
                    <div class="user-status">Online</div>
                    <div class="user-activity">Viewing dashboard</div>
                </div>
                
                <div class="user-card online">
                    <div class="user-avatar-large online">
                        J
                    </div>
                    <div class="user-name">John Doe</div>
                    <div class="user-status">Online</div>
                    <div class="user-activity">Scanning QR codes</div>
                </div>
                
                <div class="user-card online">
                    <div class="user-avatar-large online">
                        S
                    </div>
                    <div class="user-name">Sarah Wilson</div>
                    <div class="user-status">Online</div>
                    <div class="user-activity">Adding new files</div>
                </div>
                
                <div class="user-card">
                    <div class="user-avatar-large">
                        M
                    </div>
                    <div class="user-name">Mike Johnson</div>
                    <div class="user-status">Offline</div>
                    <div class="user-activity">Last seen 2 hours ago</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Activity Grid -->
    <div class="collaboration-grid">
        <!-- Real-time User Activities -->
        <div class="activity-panel">
            <div class="panel-header">
                <h3 class="panel-title">
                    <i class="fas fa-bolt me-2"></i>Real-time User Activities
                </h3>
                <span class="panel-badge" id="userActivityCount">{{ recent_activities|length or 0 }}</span>
            </div>
            <div class="panel-body">
                <ul class="activity-list" id="userActivityFeed">
                    {% for activity in recent_activities %}
                    <li class="activity-item">
                        <div class="activity-avatar">
                            {{ activity.user.username[0].upper() if activity.user else 'S' }}
                        </div>
                        <div class="activity-content">
                            <div class="activity-text">
                                <strong>{{ activity.user.username if activity.user else 'System' }}</strong>
                                {{ activity.activity_description }}
                            </div>
                            <div class="activity-meta">
                                <div class="activity-time">
                                    <i class="fas fa-clock"></i>
                                    {{ activity.timestamp.strftime('%H:%M:%S') }}
                                </div>
                                <div class="activity-status status-online">
                                    <i class="fas fa-circle"></i>
                                    {{ activity.activity_type }}
                                </div>
                            </div>
                        </div>
                    </li>
                    {% else %}
                    <li class="activity-item">
                        <div class="activity-content">
                            <div class="activity-text text-muted">
                                <i class="fas fa-info-circle me-2"></i>No recent activities
                            </div>
                        </div>
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>

        <!-- Live Activity Feed -->
        <div class="activity-panel">
            <div class="panel-header">
                <h3 class="panel-title">
                    <i class="fas fa-activity me-2"></i>File Access Log
                </h3>
                <span class="panel-badge" id="activityCount">{{ active_logs|length or 0 }}</span>
            </div>
            <div class="panel-body">
                <ul class="activity-list" id="activityFeed">
                    {% for log in active_logs %}
                    <li class="activity-item">
                        <div class="activity-avatar">
                            {{ log.user.username[0].upper() if log.user else 'S' }}
                        </div>
                        <div class="activity-content">
                            <div class="activity-text">
                                {% if log.user %}{{ log.user.username }}{% else %}System{% endif %} 
                                {{ log.action }} 
                                {% if log.file %}"{{ log.file.title }}"{% endif %}
                            </div>
                            <div class="activity-meta">
                                <div class="activity-time">
                                    <i class="fas fa-clock"></i>
                                    {{ log.timestamp.strftime('%I:%M %p') }}
                                </div>
                                <div class="activity-status status-online">
                                    <i class="fas fa-circle"></i>
                                    Live
                                </div>
                            </div>
                        </div>
                    </li>
                    {% else %}
                    <li class="activity-item">
                        <div class="empty-state">
                            <div class="empty-icon">
                                <i class="fas fa-history"></i>
                            </div>
                            <p>No recent activity</p>
                        </div>
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
        
        <!-- Notifications -->
        <div class="notifications-panel">
            <div class="notifications-header">
                <h3 class="panel-title">
                    <i class="fas fa-bell me-2"></i>Notifications
                </h3>
                <span class="panel-badge">2 New</span>
            </div>
            <div class="panel-body">
                <div class="notification-item unread">
                    <div class="notification-icon file">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <div class="notification-content">
                        <div class="notification-text">
                            <strong>John Doe</strong> accessed "Project Report.pdf"
                        </div>
                        <div class="notification-time">2 minutes ago</div>
                    </div>
                </div>
                
                <div class="notification-item unread">
                    <div class="notification-icon user">
                        <i class="fas fa-user-plus"></i>
                    </div>
                    <div class="notification-content">
                        <div class="notification-text">
                            <strong>Sarah Wilson</strong> joined the collaboration
                        </div>
                        <div class="notification-time">5 minutes ago</div>
                    </div>
                </div>
                
                <div class="notification-item">
                    <div class="notification-icon system">
                        <i class="fas fa-cog"></i>
                    </div>
                    <div class="notification-content">
                        <div class="notification-text">
                            System backup completed successfully
                        </div>
                        <div class="notification-time">1 hour ago</div>
                    </div>
                </div>
                
                <div class="notification-item">
                    <div class="notification-icon file">
                        <i class="fas fa-qrcode"></i>
                    </div>
                    <div class="notification-content">
                        <div class="notification-text">
                            QR code generated for "Budget 2024.xlsx"
                        </div>
                        <div class="notification-time">2 hours ago</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    initializeCollaboration();
});

function initializeCollaboration() {
    // Initialize Socket.IO for real-time updates
    if (typeof io !== 'undefined') {
        const socket = io();
        
        // Listen for file activity
        socket.on('file_activity', function(data) {
            addActivityToFeed(data);
            showNotification(data);
        });
        
        // Listen for user status changes
        socket.on('user_status', function(data) {
            updateUserStatus(data);
        });

        // Listen for real-time user activities
        socket.on('user_activity', function(data) {
            addUserActivityToFeed(data);
        });

        // Listen for new notifications
        socket.on('notification', function(data) {
            addNotification(data);
        });
        
        // Emit user presence
        socket.emit('user_presence', {
            user: '{{ current_user.username }}',
            status: 'online',
            activity: 'viewing collaboration'
        });
    }
}

function addActivityToFeed(data) {
    const activityFeed = document.getElementById('activityFeed');
    const activityCount = document.getElementById('activityCount');
    
    // Create new activity item
    const activityItem = document.createElement('li');
    activityItem.className = 'activity-item';
    activityItem.innerHTML = `
        <div class="activity-avatar">
            ${data.user[0].toUpperCase()}
        </div>
        <div class="activity-content">
            <div class="activity-text">
                <strong>${data.user}</strong> ${data.action} "${data.file_title}"
            </div>
            <div class="activity-meta">
                <div class="activity-time">
                    <i class="fas fa-clock"></i>
                    Just now
                </div>
                <div class="activity-status status-online">
                    <i class="fas fa-circle"></i>
                    Live
                </div>
            </div>
        </div>
    `;
    
    // Add animation
    activityItem.style.opacity = '0';
    activityItem.style.transform = 'translateX(-20px)';
    
    // Insert at the beginning
    const firstItem = activityFeed.querySelector('.activity-item');
    if (firstItem) {
        activityFeed.insertBefore(activityItem, firstItem);
    } else {
        activityFeed.appendChild(activityItem);
    }
    
    // Animate in
    setTimeout(() => {
        activityItem.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
        activityItem.style.opacity = '1';
        activityItem.style.transform = 'translateX(0)';
    }, 100);
    
    // Update count
    const currentCount = parseInt(activityCount.textContent) || 0;
    activityCount.textContent = currentCount + 1;
    
    // Remove items if more than 10
    const items = activityFeed.querySelectorAll('.activity-item');
    if (items.length > 10) {
        items[items.length - 1].remove();
    }
}

function updateUserStatus(data) {
    // Update user status in the online users section
    const userCards = document.querySelectorAll('.user-card');
    userCards.forEach(card => {
        const userName = card.querySelector('.user-name').textContent.split(' (')[0];
        if (userName === data.user) {
            const statusElement = card.querySelector('.user-status');
            const activityElement = card.querySelector('.user-activity');
            const avatar = card.querySelector('.user-avatar-large');
            
            statusElement.textContent = data.status;
            activityElement.textContent = data.activity;
            
            if (data.status === 'online') {
                card.classList.add('online');
                avatar.classList.add('online');
            } else {
                card.classList.remove('online');
                avatar.classList.remove('online');
            }
        }
    });
}

function addNotification(data) {
    const notificationsBody = document.querySelector('.notifications-panel .panel-body');
    
    const notification = document.createElement('div');
    notification.className = 'notification-item unread';
    notification.innerHTML = `
        <div class="notification-icon ${data.type}">
            <i class="fas fa-${data.icon}"></i>
        </div>
        <div class="notification-content">
            <div class="notification-text">${data.message}</div>
            <div class="notification-time">Just now</div>
        </div>
    `;
    
    // Insert at the beginning
    const firstNotification = notificationsBody.querySelector('.notification-item');
    if (firstNotification) {
        notificationsBody.insertBefore(notification, firstNotification);
    } else {
        notificationsBody.appendChild(notification);
    }
    
    // Update badge count
    const badge = document.querySelector('.notifications-header .panel-badge');
    const currentCount = parseInt(badge.textContent.split(' ')[0]) || 0;
    badge.textContent = `${currentCount + 1} New`;
}

function addUserActivityToFeed(data) {
    const userActivityFeed = document.getElementById('userActivityFeed');
    const userActivityCount = document.getElementById('userActivityCount');

    // Create new user activity item
    const activityItem = document.createElement('li');
    activityItem.className = 'activity-item';
    activityItem.innerHTML = `
        <div class="activity-avatar">
            ${data.username[0].toUpperCase()}
        </div>
        <div class="activity-content">
            <div class="activity-text">
                <strong>${data.username}</strong> ${data.activity_description}
            </div>
            <div class="activity-meta">
                <div class="activity-time">
                    <i class="fas fa-clock"></i>
                    Just now
                </div>
                <div class="activity-status status-online">
                    <i class="fas fa-circle"></i>
                    ${data.activity_type}
                </div>
            </div>
        </div>
    `;

    // Add to top of feed
    if (userActivityFeed.firstChild) {
        userActivityFeed.insertBefore(activityItem, userActivityFeed.firstChild);
    } else {
        userActivityFeed.appendChild(activityItem);
    }

    // Remove old items (keep only last 20)
    while (userActivityFeed.children.length > 20) {
        userActivityFeed.removeChild(userActivityFeed.lastChild);
    }

    // Update count
    const currentCount = parseInt(userActivityCount.textContent) || 0;
    userActivityCount.textContent = currentCount + 1;

    // Add animation
    activityItem.style.opacity = '0';
    activityItem.style.transform = 'translateY(-10px)';
    setTimeout(() => {
        activityItem.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
        activityItem.style.opacity = '1';
        activityItem.style.transform = 'translateY(0)';
    }, 100);
}

function showNotification(data) {
    // Show browser notification if permission granted
    if (Notification.permission === 'granted') {
        new Notification(`T-Office: ${data.action}`, {
            body: `${data.user} ${data.action} "${data.file_title}"`,
            icon: '/static/images/icon-192x192.png',
            tag: 'toffice-activity'
        });
    }
    
    // Show in-app notification
    if (window.TOffice && window.TOffice.showNotification) {
        TOffice.showNotification(
            `${data.user} ${data.action} "${data.file_title}"`, 
            'info', 
            3000
        );
    }
}

// Mark notifications as read when clicked
document.addEventListener('click', function(e) {
    if (e.target.closest('.notification-item.unread')) {
        const notification = e.target.closest('.notification-item');
        notification.classList.remove('unread');
        
        // Update badge count
        const badge = document.querySelector('.notifications-header .panel-badge');
        const currentCount = parseInt(badge.textContent.split(' ')[0]) || 0;
        if (currentCount > 0) {
            const newCount = currentCount - 1;
            badge.textContent = newCount > 0 ? `${newCount} New` : '0 New';
        }
    }
});

// Request notification permission
if ('Notification' in window && Notification.permission === 'default') {
    Notification.requestPermission();
}

// Track collaboration page view
if (window.TOffice && window.TOffice.trackEvent) {
    TOffice.trackEvent('collaboration_view');
}
</script>
{% endblock %}

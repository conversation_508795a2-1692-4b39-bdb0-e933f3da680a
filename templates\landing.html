{% extends "base.html" %}

{% block title %}Taluk Office - Digital File Management System{% endblock %}

{% block content %}
<div class="landing-page">
    <!-- Official Government Header -->
    <section class="govt-header">
        <div class="container">
            <div class="row align-items-center py-4">
                <div class="col-md-2 text-center">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/5/55/Emblem_of_India.svg"
                         alt="Government of India" class="govt-emblem">
                </div>
                <div class="col-md-8 text-center">
                    <h1 class="govt-title">Government of Karnataka</h1>
                    <h2 class="dept-title">Taluk Office</h2>
                    <p class="dept-subtitle">Digital File Management System</p>
                </div>
                <div class="col-md-2 text-center">
                    <img src="https://upload.wikimedia.org/wikipedia/commons/thumb/9/91/Seal_of_Karnataka.svg/200px-Seal_of_Karnataka.svg.png"
                         alt="Karnataka Government" class="state-emblem">
                </div>
            </div>
        </div>
    </section>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <div class="hero-content">
                        <!-- Karnataka Government Logo -->
                        <div class="govt-logo-container mb-4">
                            <img src="{{ url_for('static', filename='images/karnataka-govt-logo.svg') }}"
                                 alt="Government of Karnataka Logo"
                                 class="govt-logo">
                        </div>
                        <h1 class="hero-title">
                            Digital Transformation Initiative
                        </h1>
                        <p class="hero-description">
                            Modernizing government file management through digital technology.
                            Secure, efficient, and transparent document management system
                            with advanced QR code tracking and bundle organization.
                        </p>
                        <div class="hero-actions">
                            {% if current_user.is_authenticated %}
                            <a href="{{ url_for('dashboard') }}" class="btn btn-govt btn-lg me-3">
                                <i class="fas fa-tachometer-alt me-2"></i>Access Dashboard
                            </a>
                            {% else %}
                            <a href="{{ url_for('login') }}" class="btn btn-govt btn-lg me-3">
                                <i class="fas fa-sign-in-alt me-2"></i>Official Login
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features-section">
        <div class="container">
            <div class="section-header text-center">
                <h2>Key Features</h2>
                <p>Comprehensive digital file management for government offices</p>
            </div>
            
            <div class="row">
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-qrcode"></i>
                        </div>
                        <h4>QR Code Management</h4>
                        <p>Generate and scan QR codes for instant file access and location tracking.</p>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-archive"></i>
                        </div>
                        <h4>Bundle Organization</h4>
                        <p>Organize files into bundles with rack-based storage system for easy retrieval.</p>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-file-excel"></i>
                        </div>
                        <h4>Excel Integration</h4>
                        <p>Bulk upload and manage files through Excel imports with automatic organization.</p>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <h4>Advanced Search</h4>
                        <p>Search files by village, survey number, reference ID, or any document attribute.</p>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h4>Secure Access</h4>
                        <p>Role-based access control with comprehensive audit trails and security features.</p>
                    </div>
                </div>
                
                <div class="col-md-4 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-bar"></i>
                        </div>
                        <h4>Analytics & Reports</h4>
                        <p>Generate detailed reports and analytics for file usage and system performance.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Statistics Section -->
    {% if current_user.is_authenticated %}
    <section class="stats-section">
        <div class="container">
            <div class="section-header text-center">
                <h2>System Overview</h2>
                <p>Current system statistics and usage</p>
            </div>
            
            <div class="row">
                <div class="col-md-3 mb-4">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-file-alt"></i>
                        </div>
                        <div class="stat-number">{{ total_files or 0 }}</div>
                        <div class="stat-label">Total Files</div>
                    </div>
                </div>
                
                <div class="col-md-3 mb-4">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-archive"></i>
                        </div>
                        <div class="stat-number">{{ total_bundles or 0 }}</div>
                        <div class="stat-label">Bundles</div>
                    </div>
                </div>
                
                <div class="col-md-3 mb-4">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="stat-number">{{ total_villages or 0 }}</div>
                        <div class="stat-label">Villages</div>
                    </div>
                </div>
                
                <div class="col-md-3 mb-4">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-qrcode"></i>
                        </div>
                        <div class="stat-number">{{ total_qr_codes or 0 }}</div>
                        <div class="stat-label">QR Codes</div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    {% endif %}

    <!-- Government Notice Section -->
    <section class="notice-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-10 mx-auto">
                    <div class="govt-notice">
                        <div class="notice-header">
                            <i class="fas fa-info-circle"></i>
                            <h3>Official Notice</h3>
                        </div>
                        <div class="notice-content">
                            <p>This is an official government system for digital file management. Access is restricted to authorized personnel only. All activities are logged and monitored for security and compliance purposes.</p>
                            <p><strong>For technical support or access requests, please contact the system administrator.</strong></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer Section -->
    <section class="footer-section">
        <div class="container">
            <div class="row">
                <div class="col-lg-12 text-center">
                    <div class="footer-content">
                        <p>&copy; 2024 Government of Karnataka - Taluk Office Digital File Management System</p>
                        <p>Developed under Digital India Initiative</p>
                        {% if current_user.is_authenticated %}
                        <a href="{{ url_for('dashboard') }}" class="btn btn-govt btn-sm">
                            <i class="fas fa-arrow-right me-2"></i>Access System
                        </a>
                        {% else %}
                        <a href="{{ url_for('login') }}" class="btn btn-govt btn-sm">
                            <i class="fas fa-sign-in-alt me-2"></i>Official Login
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
/* Import official color variables */
:root {
    --primary-color: #003366;
    --primary-dark: #002244;
    --primary-light: #004488;
    --secondary-color: #8B0000;
    --success-color: #006600;
    --warning-color: #CC6600;
    --danger-color: #CC0000;
    --info-color: #004488;
    --gold-color: #FFD700;
    --white: #ffffff;
    --gray-50: #f8f9fa;
    --gray-100: #f1f3f4;
    --gray-200: #e8eaed;
    --gray-300: #dadce0;
    --gray-400: #9aa0a6;
    --gray-500: #5f6368;
    --gray-600: #3c4043;
    --gray-700: #202124;
    --gray-800: #1a1a1a;
    --gray-900: #000000;
}

.landing-page {
    background: #ffffff;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Government Header */
.govt-header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    border-bottom: 3px solid var(--gold-color);
}

.govt-emblem, .state-emblem {
    width: 80px;
    height: 80px;
    filter: brightness(0) invert(1);
}

.govt-title {
    font-size: 2.2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: white;
}

.dept-title {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 0.3rem;
    color: #fff3e6;
}

.dept-subtitle {
    font-size: 1.1rem;
    color: #ffe6cc;
    margin-bottom: 0;
}

/* Hero Section */
.hero-section {
    padding: 4rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border-bottom: 1px solid #dee2e6;
}

/* Government Logo Styling */
.govt-logo-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.govt-logo {
    width: 120px;
    height: 120px;
    filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
    transition: transform 0.3s ease;
}

.govt-logo:hover {
    transform: scale(1.05);
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1.5rem;
    color: #2c3e50;
}

.hero-description {
    font-size: 1.1rem;
    color: #495057;
    margin-bottom: 2rem;
    line-height: 1.6;
}

.btn-govt {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    border: none;
    color: white;
    font-weight: 600;
    padding: 12px 30px;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.btn-govt:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 100%);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 51, 102, 0.3);
}

/* Features Section */
.features-section {
    padding: 4rem 0;
    background: #f8f9fa;
}

.section-header h2 {
    font-size: 2.2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1rem;
    color: #6c757d;
}

.feature-card {
    text-align: center;
    padding: 2rem 1.5rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
    height: 100%;
    border-left: 4px solid var(--primary-color);
}

.feature-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.feature-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.feature-icon i {
    font-size: 1.8rem;
    color: white;
}

.feature-card h4 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.feature-card p {
    color: #6c757d;
    line-height: 1.5;
    font-size: 0.95rem;
}

/* Notice Section */
.notice-section {
    padding: 3rem 0;
    background: #f0f4f8;
    border-top: 1px solid var(--gray-300);
    border-bottom: 1px solid var(--gray-300);
}

.govt-notice {
    background: white;
    border: 2px solid var(--primary-color);
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 3px 15px rgba(0, 51, 102, 0.1);
}

.notice-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.notice-header i {
    font-size: 1.5rem;
    margin-right: 0.5rem;
}

.notice-header h3 {
    margin: 0;
    font-weight: 700;
}

.notice-content p {
    margin-bottom: 1rem;
    color: #495057;
    line-height: 1.6;
}

/* Footer Section */
.footer-section {
    padding: 3rem 0;
    background: #2c3e50;
    color: white;
}

.footer-content p {
    margin-bottom: 0.5rem;
    color: #bdc3c7;
}

.footer-content p:last-of-type {
    margin-bottom: 1.5rem;
}

/* CTA Section */
.cta-section {
    padding: 3rem 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.cta-section h2 {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 1rem;
}

.cta-section p {
    font-size: 1rem;
    color: #6c757d;
    margin-bottom: 2rem;
}

@media (max-width: 768px) {
    .govt-title {
        font-size: 1.8rem;
    }

    .dept-title {
        font-size: 1.4rem;
    }

    .hero-title {
        font-size: 2rem;
    }

    .section-header h2 {
        font-size: 1.8rem;
    }

    .govt-emblem, .state-emblem {
        width: 60px;
        height: 60px;
    }
}
</style>
{% endblock %}

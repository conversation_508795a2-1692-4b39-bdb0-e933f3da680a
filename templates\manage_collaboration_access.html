{% extends "base.html" %}

{% block title %}Manage Collaboration Access - Taluk Office Digital System{% endblock %}

{% block styles %}
<style>
.access-management-container {
    padding: 2rem 0;
}

.access-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    margin-bottom: 1rem;
    border-left: 4px solid #2563eb;
}

.user-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.user-details h5 {
    margin: 0;
    color: #2c3e50;
}

.user-details p {
    margin: 0.25rem 0 0 0;
    color: #6c757d;
    font-size: 0.9rem;
}

.access-status {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status-granted {
    background: #d4edda;
    color: #155724;
}

.status-denied {
    background: #f8d7da;
    color: #721c24;
}

.status-admin {
    background: #d1ecf1;
    color: #0c5460;
}

.access-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-access {
    padding: 0.375rem 0.75rem;
    border-radius: 6px;
    font-size: 0.875rem;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-grant {
    background: #28a745;
    color: white;
}

.btn-grant:hover {
    background: #218838;
    color: white;
}

.btn-revoke {
    background: #dc3545;
    color: white;
}

.btn-revoke:hover {
    background: #c82333;
    color: white;
}

.access-summary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem;
    border-radius: 12px;
    margin-bottom: 2rem;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    display: block;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-4">
    <div class="access-management-container">
        <!-- Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-0">
                    <i class="fas fa-users-cog me-2"></i>Manage Collaboration Access
                </h1>
                <p class="text-muted mt-1">Control who can access the real-time collaboration features</p>
            </div>
            <a href="{{ url_for('collaboration') }}" class="btn btn-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Collaboration
            </a>
        </div>

        <!-- Access Summary -->
        <div class="access-summary">
            <h4><i class="fas fa-chart-bar me-2"></i>Access Summary</h4>
            <div class="summary-stats">
                <div class="stat-item">
                    <span class="stat-number">{{ access_dict|length }}</span>
                    <span class="stat-label">Total Access Records</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">{{ access_dict.values()|selectattr('access_granted')|list|length }}</span>
                    <span class="stat-label">Active Access Grants</span>
                </div>
                <div class="stat-item">
                    <span class="stat-number">3</span>
                    <span class="stat-label">Maximum Non-Admin Users</span>
                </div>
            </div>
        </div>

        <!-- Users List -->
        <div class="row">
            {% for user in users %}
            <div class="col-lg-6 col-xl-4">
                <div class="access-card">
                    <div class="user-info">
                        <div class="user-details">
                            <h5>{{ user.username }}</h5>
                            <p>{{ user.email }}</p>
                            <p><strong>Role:</strong> {{ user.role }}</p>
                        </div>
                    </div>
                    
                    <div class="access-status mt-3">
                        {% if user.role == 'Administrator' or user.is_admin %}
                            <span class="status-badge status-admin">
                                <i class="fas fa-crown me-1"></i>Admin Access
                            </span>
                        {% elif user.id in access_dict and access_dict[user.id].access_granted %}
                            <span class="status-badge status-granted">
                                <i class="fas fa-check-circle me-1"></i>Access Granted
                            </span>
                        {% else %}
                            <span class="status-badge status-denied">
                                <i class="fas fa-times-circle me-1"></i>No Access
                            </span>
                        {% endif %}
                        
                        <div class="access-actions">
                            {% if user.role != 'Administrator' and not user.is_admin %}
                                {% if user.id in access_dict and access_dict[user.id].access_granted %}
                                    <a href="{{ url_for('revoke_collaboration_access', user_id=user.id) }}" 
                                       class="btn-access btn-revoke"
                                       onclick="return confirm('Are you sure you want to revoke collaboration access for {{ user.username }}?')">
                                        <i class="fas fa-user-times me-1"></i>Revoke
                                    </a>
                                {% else %}
                                    <a href="{{ url_for('grant_collaboration_access', user_id=user.id) }}" 
                                       class="btn-access btn-grant">
                                        <i class="fas fa-user-plus me-1"></i>Grant
                                    </a>
                                {% endif %}
                            {% endif %}
                        </div>
                    </div>
                    
                    {% if user.id in access_dict %}
                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="fas fa-clock me-1"></i>
                            Last updated: {{ access_dict[user.id].granted_at.strftime('%Y-%m-%d %H:%M') if access_dict[user.id].granted_at else 'Never' }}
                        </small>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Add any JavaScript for managing collaboration access
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh every 30 seconds to show real-time updates
    setTimeout(function() {
        location.reload();
    }, 30000);
});
</script>
{% endblock %}

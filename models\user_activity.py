from extensions import db
from datetime import datetime
import json

class UserActivity(db.Model):
    """Model to track real-time user activities for collaboration"""
    __tablename__ = 'user_activities'

    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.<PERSON>ey('users.id'), nullable=False)
    activity_type = db.Column(db.String(50), nullable=False)  # login, logout, file_view, file_edit, page_visit, etc.
    activity_description = db.Column(db.Text)
    page_url = db.Column(db.String(255))
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.Text)
    session_id = db.Column(db.String(100))  # Store hash of session ID if too long
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Additional metadata as JSON
    activity_metadata = db.Column(db.Text)  # JSON field for additional activity data
    
    # Relationships
    user = db.relationship('User', backref='activities')
    
    def __repr__(self):
        return f'<UserActivity {self.activity_type} by User {self.user_id}>'
    
    def set_metadata(self, metadata_dict):
        """Store metadata as JSON"""
        if metadata_dict:
            self.activity_metadata = json.dumps(metadata_dict)

    def get_metadata(self):
        """Retrieve metadata as dictionary"""
        if self.activity_metadata:
            try:
                return json.loads(self.activity_metadata)
            except json.JSONDecodeError:
                return {}
        return {}

class CollaborationAccess(db.Model):
    """Model to manage collaboration access control"""
    __tablename__ = 'collaboration_access'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False, unique=True)
    access_granted = db.Column(db.Boolean, default=False)
    granted_by = db.Column(db.Integer, db.ForeignKey('users.id'))
    granted_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_accessed = db.Column(db.DateTime)
    access_count = db.Column(db.Integer, default=0)
    
    # Relationships
    user = db.relationship('User', foreign_keys=[user_id], backref='collaboration_access')
    granted_by_user = db.relationship('User', foreign_keys=[granted_by])
    
    def __repr__(self):
        return f'<CollaborationAccess User {self.user_id} - {"Granted" if self.access_granted else "Denied"}>'
